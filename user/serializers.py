import json
from rest_framework import serializers
from django.utils import timezone
from django.core.exceptions import ValidationError as DjangoValidationError
from user.services import UserStatusService
from user.utils.user_password import (
    MinimumLengthValidator,
    UppercaseValidator,
    LowercaseValidator,
    NumberValidator,
    SpecialCharacterValidator
)
from .models import Company, User, Role, Permission, UserRole, RolePermission, UserStatusLog, Department, UserTag
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.password_validation import validate_password
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.validators import ValidationError
# from rest_framework.authtoken.models import Token
from rest_framework_simplejwt.tokens import RefreshToken, TokenError
from llm_rag_doc.serializers import CompanySerializer, CompanyBasicSerializer
from linechatbot.models import LineUserProfile
from linechatbot.serializers import LineUserProfileSerializer


class DepartmentSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
    )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
    )
    
    class Meta:
        model = Department
        fields = '__all__'

class UserDepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ['id', 'name', 'code', 'color']

class UserTagBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserTag
        fields = ['id', 'name', 'color']

class UserSerializer(serializers.ModelSerializer):
    partners = CompanyBasicSerializer(many=True, read_only=True)
    departments = UserDepartmentSerializer(many=True, read_only=True)
    user_tags = UserTagBasicSerializer(many=True, read_only=True)
    roles = serializers.SerializerMethodField()
    password = serializers.CharField(max_length=50, min_length=8, write_only=True)
    confirm_password = serializers.CharField(max_length=50, min_length=8, write_only=True)
    line_user = LineUserProfileSerializer(source='line_user_id', read_only=True)

    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )

    class Meta:
        model = User
        # TODO - Select columns before release
        fields = '__all__'
        # TODO - exlude passowrd while successfully validated_data.pop)
        # exclude = ['password']


    def get_roles(self, obj):
        """
        Fetch and format the user's roles
        """
        user_roles = obj.userrole_set.all().select_related('role_id')
        # print(f"UserSerializer's user_roles - {user_roles}")
        return [
            {
                'id': ur.role_id.role_id,
                'name': ur.role_id.name,
                'definition': ur.role_id.definition
            } 
            for ur in user_roles
        ]

    def validate(self, attrs):
        if self.instance is None:  # Only for new users

            user_id = self.instance.id if self.instance else None

            print(f"user serializers's validate's user_id - {user_id} (new user)")

            username_exists = User.objects.filter(username=attrs["username"]).exclude(id=user_id).exists()
            email_exists = User.objects.filter(email=attrs["email"]).exclude(id=user_id).exists()

            password = attrs.get('password')
            confirm_password = attrs.get('confirm_password')



            if password != confirm_password:
                raise ValidationError({"password": "Passwords do not match (validate)"})
            if username_exists:
                raise ValidationError({"username": "This username is already taken"})
            if email_exists:
                raise ValidationError({"email": "This email is already registered with another user"})
        else: # Only for existing users
            print(f"UserSerializer's validate's CHECKPOINT 02")
            user_id = self.instance.id if self.instance else None
            
            print(f"user serializers's validate's user_id - {user_id} (exsiting user)")

            # # Check if any other user has this email (excluding the current user)
            # if 'email' in attrs:
            #     new_email = attrs['email']
            #     email_exists = User.objects.filter(email=new_email).exclude(id=user_id).exists()
            #     if email_exists:
            #         raise ValidationError({"email": "This email is already registered with another user"})
                
            # Check if any other user has this work_email (excluding the current user)
            if 'work_email' in attrs:
                new_work_email= attrs['work_email']
                email_exists = User.objects.filter(work_email=new_work_email).exclude(id=user_id).exists()
                # TODO - Delete this or Log this
                print(f"UserSerializer's validate's new_work_email - {new_work_email}")
                print(f"UserSerializer's validate's email_exists - {email_exists} (work_email)")
                if email_exists:
                    raise ValidationError({"work_email": "This work email is already registered with another user"})


        return super().validate(attrs)

    def create(self, validated_data):
        validated_data.pop('confirm_password', None)
        password = validated_data.pop("password")
        user = super().create(validated_data)
        user.set_password(password)
        user.save()

        # Token.objects.create(user=user)

        return user

    def update(self, instance, validated_data):
        user_id = self.instance.id if self.instance else None
        password = validated_data.pop('password', None)
        confirm_password = validated_data.pop('confirm_password', None)
        # email_exists = User.objects.filter(email=attrs["email"]).exclude(id=user_id).exists()

        user = super().update(instance, validated_data)
        
        # print(f"Backend user-update function's instance - {self.instance}")

        if password:
            if password != confirm_password:
                raise ValidationError({"password": "Passwords do not match (update)"})
            user.set_password(password)
            user.save()

        return user

class UserBasicSerializer(serializers.ModelSerializer):
    """Simple serializer for basic user information"""
    departments = serializers.SerializerMethodField()
    roles = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'name', 'employee_id', 'departments', 'roles']
    
    def get_roles(self, obj):
        role_names = UserRole.objects.filter(user_id=obj).values_list('role_id__name', flat=True)
        return ", ".join(role_names)

    def get_departments(self, obj):
        departments_values = obj.departments.values('name', 'code', 'color')
        return departments_values

# class LoginSerializer(serializers.ModelSerializer):
#     username = serializers.CharField(max_length=150)
#     password = serializers.CharField(max_length=50, min_length=6, write_only=True)
#     access_token = serializers.CharField(max_length=255, read_only=True)
#     refresh_token = serializers.CharField(max_length=255, read_only=True)

#     class Meta:
#         model = User
#         fields = ['username', 'password', 'access_token', 'refresh_token']

#     def validate(self, attrs):
#         username = attrs.get('username')
#         password = attrs.get('password')
#         request = self.context.get('request')

#         # Authenticate user
#         user = authenticate(request=request, username=username, password=password)
#         if not user:
#             raise AuthenticationFailed('Invalid username or password')

#         # Call Django login to trigger signal
#         login(request, user)

#         user_tokens = user.tokens()

#         return {
#             'username': user.username,
#             'access_token': str(user_tokens.get('access')),
#             'refresh_token': str(user_tokens.get('refresh'))
#         }

class LoginSerializer(serializers.ModelSerializer):
    username = serializers.CharField(max_length=150)
    password = serializers.CharField(max_length=50, min_length=6, write_only=True)
    access_token = serializers.CharField(max_length=255, read_only=True)
    refresh_token = serializers.CharField(max_length=255, read_only=True)
    role = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        fields = ['username', 'password', 'access_token', 'refresh_token', 'role']

    def get_role(self, obj):
        """Get the roles of the user"""
        # Use the authenticated user stored in the serializer
        if not hasattr(self, 'user'):
            return None
            
        user_roles = UserRole.objects.filter(user_id=self.user)
        if not user_roles.exists():
            return None
        
        # Return the first role for simplicity - you can modify to return all roles if needed
        return {
            'id': user_roles.first().role_id.role_id,
            'name': user_roles.first().role_id.name
        }

    # def validate(self, attrs):
    #     username = attrs.get('username')
    #     password = attrs.get('password')
    #     request = self.context.get('request')

    #     # Authenticate user
    #     user = authenticate(request=request, username=username, password=password)
    #     if not user:
    #         raise AuthenticationFailed('Invalid username or password')

    #     # Call Django login to trigger signal
    #     login(request, user)

    #     user_tokens = user.tokens()
        
    #     # Store the authenticated user on the serializer instance
    #     # so we can access it in get_role method
    #     self.user = user

    #     return {
    #         'username': user.username,
    #         'access_token': str(user_tokens.get('access')),
    #         'refresh_token': str(user_tokens.get('refresh'))
    #     }

    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        request = self.context.get('request')
        
        # Authenticate user
        user = authenticate(request=request, username=username, password=password)
        if not user:
            raise AuthenticationFailed('Invalid username or password')
        
        # Update user status to ONLINE
        UserStatusService.update_user_status(
            user_id=user.id,
            status=User.StatusChoices.ONLINE,
            is_auto=True
        )
        print(f"LoginSerializer's validate method - User {user.username} logged in, status updated to ONLINE")
        
        user_tokens = user.tokens()
        self.user = user
        
        return {
            'username': user.username,
            'access_token': str(user_tokens.get('access')),
            'refresh_token': str(user_tokens.get('refresh'))
        }

class LogoutSerializer(serializers.Serializer):
    refresh_token = serializers.CharField()
    # default_error_message = {'bad_token': ('Token is invalid or has expired')}
    # error_messages = {'bad_token': ('Token is invalid or has expired')}

    def validate(self, attrs):
        self.token = attrs.get('refresh_token')
        return attrs

    def save(self, **kwargs):
        try:
            # Blacklist the token
            token = RefreshToken(self.token)
            token.blacklist()
            
            # Call Django logout to trigger signal
            request = self.context.get('request')
            if request and request.user.is_authenticated:
                # logout(request)
                UserStatusService.update_user_status(
                    user_id=request.user.id,
                    status=User.StatusChoices.OFFLINE,
                    is_auto=True
                )
                print(f"LogoutSerializer's save method - Logging out user: {request.user.username}, status updated to OFFLINE")
                
        except TokenError:
            # return self.fail('bad_token')
            raise serializers.ValidationError('Token is invalid or expired')
            

class RoleSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    
    class Meta:
        model = Role
        fields = '__all__'

class PermissionSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    
    class Meta:
        model = Permission
        fields = '__all__'

class UserRoleSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(source='role_id.name', read_only=True)
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    
    class Meta:
        model = UserRole
        fields = '__all__'

class RolePermissionSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    
    class Meta:
        model = RolePermission
        fields = '__all__'

class UserDeleteSerializer(serializers.Serializer):
    username = serializers.CharField(max_length=150)
    confirm_username = serializers.CharField(max_length=150)

    def validate(self, attrs):
        username = attrs.get('username')
        confirm_username = attrs.get('confirm_username')

        print(f"UserDeleteSerializer's username - {username}")
        print(f"UserDeleteSerializer's confirm_username - {confirm_username}")

        if username != confirm_username:
            raise ValidationError({"username": "Username confirmation does not match"})

        return attrs

class UserStatusLogSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
        )
    
    class Meta:
        model = UserStatusLog
        fields = '__all__'

class RoleAssignmentSerializer(serializers.Serializer):
    role_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True
    )
    
    def validate_role_ids(self, value):
        existing_roles = Role.objects.filter(role_id__in=value, is_active=True)
        if len(existing_roles) != len(value):
            missing_ids = set(value) - set(existing_roles.values_list('role_id', flat=True))
            raise serializers.ValidationError(f"Roles with ids {missing_ids} do not exist or are inactive")
        return value

class UserRoleDetailSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(source='role_id.name')
    
    class Meta:
        model = UserRole
        fields = ['role_name']

class UsersInfosSerializer(serializers.ModelSerializer):
    from setting.serializers import UserScheduleSerializer

    roles = serializers.SerializerMethodField()
    # partners = CompanySerializer(many=True)
    partners = serializers.SerializerMethodField()
    departments = serializers.SerializerMethodField()
    tags = serializers.SerializerMethodField()
    line_user_name = serializers.CharField(source='line_user_id.display_name', read_only=True)
    work_schedule = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = '__all__'
    
    def get_roles(self, obj):
        user_roles = UserRole.objects.filter(user_id=obj)
        # return UserRoleDetailSerializer(user_roles, many=True).data
        # Join role names with commas
        # role_names = user_roles.values_list('name', flat=True)
        # return ", ".join(role_names)
        role_names = UserRole.objects.filter(user_id=obj)\
            .values_list('role_id__name', flat=True)
        return ", ".join(role_names)
    
    def get_partners(self, obj):
        # Join partner names with commas
        # partner_names = obj.partners.values_list('name', flat=True)
        # return ", ".join(partner_names)
        # TODO - Delete this
        # print(f"get_partners's partner_names (values) - {obj.partners.values('name', 'code')}")
        # print(f"get_partners's partner_names (values_list) - {obj.partners.values_list('name', 'code')}")
        # partners_value = obj.partners.values_list('name', 'code')
        partners_values = obj.partners.values('name', 'code', 'color')
        return partners_values
    
    def get_departments(self, obj):
        # Join department names with commas
        # department_names = obj.departments.values_list('name', flat=True)
        # return ", ".join(department_names)
        # TODO - Delete this
        # print(f"get_partners's departments - {obj.departments}")
        departments_values = obj.departments.values('name', 'code', 'color')
        return departments_values

    def get_tags(self, obj):
        tags_values = obj.user_tags.values('id', 'name', 'color')
        return tags_values
    
    # Method to get work schedule with business hours logic
    def get_work_schedule(self, obj):
        """
        Get user's work schedule. If same_as_business_hours is True,
        return company business hours instead of user's custom schedule.
        """
        # Check if user has a work schedule
        if not hasattr(obj, 'work_schedule') or not obj.work_schedule:
            return None
        
        user_schedule = obj.work_schedule
        
        # Determine which schedule data to use
        if user_schedule.same_as_business_hours:
            # Use cached company business hours
            schedule_data = self._get_company_business_hours()
        else:
            # Use user's custom schedule
            schedule_data = user_schedule.schedule
        
        # Return formatted work schedule
        return {
            'same_as_business_hours': user_schedule.same_as_business_hours,
            'schedule': schedule_data,
            'created_on': user_schedule.created_on.isoformat() if user_schedule.created_on else None,
            'updated_on': user_schedule.updated_on.isoformat() if user_schedule.updated_on else None
        }
    
    # Helper method to get company business hours with caching
    def _get_company_business_hours(self):
        """
        Get company business hours from SystemSettings with caching.
        Cache expires after 1 hour (3600 seconds).
        """
        from setting.services import SettingsService

        # Fetch from database
        # business_hours_setting = SystemSettings.objects.get(
        #     key='COMPANY_BUSINESS_HOURS'
        # )
        business_hours_setting = SettingsService.get_setting('COMPANY_BUSINESS_HOURS')

        print(f"_get_company_business_hours's business_hours_setting - {business_hours_setting}")
        
        # Parse JSON value
        if business_hours_setting:
            # business_hours = json.loads(business_hours_setting.value)
            business_hours = json.loads(business_hours_setting)
        else:
            business_hours = self._get_default_business_hours()
        
        return business_hours
    
    # Helper method to get default business hours structure
    def _get_default_business_hours(self):
        """
        Return default business hours structure as fallback.
        """
        return {
            "sameAsBusinessHours": False,
            "workShift": [
                {"day": "Sunday", "active": False, "times": []},
                {"day": "Monday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                {"day": "Tuesday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                {"day": "Wednesday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                {"day": "Thursday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                {"day": "Friday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                {"day": "Saturday", "active": False, "times": []},
            ]
        }
    
class UserLineAccountUpdateSerializer(serializers.Serializer):
    line_user_id = serializers.CharField(required=True)

    def validate_line_user_id(self, value):
        """
        Validate that the line_user_id exists in LineUserProfile model
        """
        try:
            LineUserProfile.objects.get(line_user_id=value)
            return value
        except LineUserProfile.DoesNotExist:
            raise serializers.ValidationError("LineUserProfile with this ID does not exist")
        
class UserTagSerializer(serializers.ModelSerializer):
    created_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
    )
    updated_by = serializers.StringRelatedField(
        default=serializers.CurrentUserDefault(), 
        read_only=True
    )
    
    class Meta:
        model = UserTag
        fields = '__all__'

# class PasswordChangeSerializer(serializers.Serializer):
#     """
#     Serializer for password change endpoint.
#     """
#     old_password = serializers.CharField(required=True)
#     new_password = serializers.CharField(required=True)
#     confirm_password = serializers.CharField(required=True)

#     def validate_old_password(self, value):
#         user = self.context['request'].user
#         if not user.check_password(value):
#             raise serializers.ValidationError("Current password is incorrect")
#         return value

#     def validate(self, data):
#         """
#         Validate that the passwords match and meet requirements
#         """
#         # If admin is changing another user's password, no need to check old password
#         request_user = self.context['request'].user
#         target_user_id = self.context.get('user_id')
        
#         if request_user.is_staff and target_user_id and str(request_user.id) != str(target_user_id):
#             # Admin changing someone else's password - no need to validate old password
#             pass
#         elif not data.get('old_password'):
#             raise serializers.ValidationError({"old_password": "Current password is required"})

#         # Check new passwords match
#         if data['new_password'] != data['confirm_password']:
#             raise serializers.ValidationError({"confirm_password": "New passwords don't match"})
        
#         # Validate password strength
#         try:
#             validate_password(data['new_password'])
#         except Exception as e:
#             raise serializers.ValidationError({"new_password": list(e)})
            
#         return data
        
#     def save(self):
#         """
#         Update the user's password
#         """
#         user_id = self.context.get('user_id')
#         if user_id:
#             user = User.objects.get(id=user_id)
#         else:
#             user = self.context['request'].user
            
#         user.set_password(self.validated_data['new_password'])
#         user.save()
#         return user

# class PasswordChangeSerializer(serializers.Serializer):
#     """
#     Serializer for password change endpoint.
#     """
#     old_password = serializers.CharField(required=False, allow_blank=True)
#     new_password = serializers.CharField(required=True)
#     confirm_password = serializers.CharField(required=True)

#     def validate(self, data):
#         """
#         Validate that the passwords match and meet requirements
#         """
#         # If admin is changing another user's password, no need to check old password
#         request_user = self.context['request'].user
#         target_user_id = self.context.get('user_id')
        
#         # Check if admin is changing someone else's password
#         # is_admin_changing_other_password = (request_user.is_staff or request_user.is_superuser) and \
#         is_admin_changing_other_password = (request_user.is_superuser) and \
#                                           target_user_id and \
#                                           str(request_user.id) != str(target_user_id)
                                          
#         # Only validate old password if user is changing their own password
#         if not is_admin_changing_other_password:
#             if not data.get('old_password'):
#                 raise serializers.ValidationError({"old_password": "Current password is required"})
                
#             # Verify old password
#             user = self.context['request'].user
#             if not user.check_password(data.get('old_password', '')):
#                 raise serializers.ValidationError({"old_password": "Current password is incorrect"})

#         # Check new passwords match
#         if data['new_password'] != data['confirm_password']:
#             raise serializers.ValidationError({"confirm_password": "New passwords don't match"})
        
#         # Validate password strength
#         try:
#             validate_password(data['new_password'])
#         except Exception as e:
#             raise serializers.ValidationError({"new_password": list(e)})
            
#         return data
        
#     def save(self):
#         """
#         Update the user's password
#         """
#         user_id = self.context.get('user_id')
#         if user_id:
#             user = User.objects.get(id=user_id)
#         else:
#             user = self.context['request'].user
            
#         user.set_password(self.validated_data['new_password'])
#         user.save()
#         return user

class PasswordChangeSerializer(serializers.Serializer):
    """
    Serializer for password change endpoint with metadata support.
    """
    old_password = serializers.CharField(required=False, allow_blank=True)
    new_password = serializers.CharField(required=True)
    confirm_password = serializers.CharField(required=True)
    
    _validation_metadata = {}

    def validate(self, data):
        """
        Validate that the passwords match and meet requirements
        """
        # Initialize metadata
        self._validation_metadata = {
            'old_password_incorrect': False,
            'passwords_match': True,
            'password_min_length_error': False,
            'password_no_uppercase_error': False,
            'password_no_lowercase_error': False,
            'password_no_number_error': False,
            'password_no_special_char_error': False,
            'password_valid': True,
            'is_admin_changing_other_password': False
        }
        
        # If admin is changing another user's password, no need to check old password
        request_user = self.context['request'].user
        target_user_id = self.context.get('user_id')
        
        # Check if admin is changing someone else's password
        is_admin_changing_other_password = (request_user.is_superuser) and \
                                          target_user_id and \
                                          str(request_user.id) != str(target_user_id)
        
        self._validation_metadata['is_admin_changing_other_password'] = is_admin_changing_other_password
                                          
        # Only validate old password if user is changing their own password
        if not is_admin_changing_other_password:
            if not data.get('old_password'):
                self._validation_metadata['old_password_incorrect'] = True
                raise serializers.ValidationError({"old_password": "Current password is required"})
                
            # Verify old password
            user = self.context['request'].user
            if not user.check_password(data.get('old_password', '')):
                self._validation_metadata['old_password_incorrect'] = True
                raise serializers.ValidationError({"old_password": "Current password is incorrect"})

        # Check new passwords match
        if data['new_password'] != data['confirm_password']:
            self._validation_metadata['passwords_match'] = False
            raise serializers.ValidationError({"confirm_password": "New passwords don't match"})
        
        # Validate each password requirement individually
        password = data['new_password']
        validators = [
            ('password_min_length_error', MinimumLengthValidator(min_length=8)),
            ('password_no_uppercase_error', UppercaseValidator()),
            ('password_no_lowercase_error', LowercaseValidator()),
            ('password_no_number_error', NumberValidator()),
            ('password_no_special_char_error', SpecialCharacterValidator(special_characters="!@#$%^&*")),
        ]
        
        password_errors = []
        for flag_name, validator in validators:
            try:
                validator.validate(password)
            except DjangoValidationError as e:
                self._validation_metadata[flag_name] = True
                self._validation_metadata['password_valid'] = False
                password_errors.extend(e.messages if hasattr(e, 'messages') else [str(e)])
        
        if password_errors:
            raise serializers.ValidationError({"new_password": password_errors})
            
        return data
    
    def get_validation_metadata(self):
        """Return the validation metadata"""
        return self._validation_metadata
        
    def save(self):
        """
        Update the user's password
        """
        from django.utils import timezone
        
        user_id = self.context.get('user_id')
        if user_id:
            user = User.objects.get(id=user_id)
        else:
            user = self.context['request'].user
            
        user.set_password(self.validated_data['new_password'])
        user.password_changed_on = timezone.now()
        user.force_password_change = False
        user.save()
        return user

# class UserSignUpSerializer(serializers.ModelSerializer):
#     """
#     Serializer for user signup with password validation
#     """
#     password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
#     confirm_password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    
#     # Store validation metadata
#     _validation_metadata = {}
    
#     class Meta:
#         model = User
#         fields = [
#             'id', 'username', 'email', 'password', 'confirm_password',
#             'first_name', 'last_name', 'name', 'employee_id',
#             'work_email', 'work_phone', 'job_title', 'hire_date',
#             'employment_type', 'preferred_language', 'is_active'
#         ]
#         extra_kwargs = {
#             'email': {'required': True},
#             'username': {'required': True},
#             'name': {'required': True},
#         }
    
#     def validate_username(self, value):
#         """Check if username already exists"""
#         self._validation_metadata['duplicated_username'] = User.objects.filter(username=value).exists()
#         if self._validation_metadata['duplicated_username']:
#             raise serializers.ValidationError("Username already exists")
#         return value
    
#     def validate_email(self, value):
#         """Check if email already exists"""
#         self._validation_metadata['duplicated_email'] = User.objects.filter(email=value).exists()
#         if self._validation_metadata['duplicated_email']:
#             raise serializers.ValidationError("Email already exists")
#         return value
    
#     def validate(self, attrs):
#         """Validate passwords match and meet requirements"""
#         password = attrs.get('password')
#         confirm_password = attrs.get('confirm_password')
        
#         # Initialize all validation flags
#         self._validation_metadata.update({
#             'passwords_match': True,
#             'password_min_length_error': False,
#             'password_no_uppercase_error': False,
#             'password_no_lowercase_error': False,
#             'password_no_number_error': False,
#             'password_no_special_char_error': False,
#             'password_valid': True
#         })
        
#         # Check if passwords match
#         if password != confirm_password:
#             self._validation_metadata['passwords_match'] = False
#             raise serializers.ValidationError({
#                 "confirm_password": "Passwords don't match"
#             })
        
#         # Validate each password requirement individually
#         validators = [
#             ('password_min_length_error', MinimumLengthValidator(min_length=8)),
#             ('password_no_uppercase_error', UppercaseValidator()),
#             ('password_no_lowercase_error', LowercaseValidator()),
#             ('password_no_number_error', NumberValidator()),
#             ('password_no_special_char_error', SpecialCharacterValidator(special_characters="!@#$%^&*")),
#         ]
        
#         password_errors = []
#         for flag_name, validator in validators:
#             try:
#                 validator.validate(password)
#             except DjangoValidationError as e:
#                 self._validation_metadata[flag_name] = True
#                 self._validation_metadata['password_valid'] = False
#                 password_errors.extend(e.messages if hasattr(e, 'messages') else [str(e)])
        
#         if password_errors:
#             raise serializers.ValidationError({"password": password_errors})
        
#         return attrs
    
#     def create(self, validated_data):
#         """Create user with validated password"""
#         # Remove confirm_password from validated_data
#         validated_data.pop('confirm_password', None)
#         password = validated_data.pop('password')
        
#         # Create user instance
#         user = User.objects.create(**validated_data)
        
#         # Set password using Django's password hashing
#         user.set_password(password)
#         user.save()
        
#         return user
    
#     def get_validation_metadata(self):
#         """Return the validation metadata"""
#         return self._validation_metadata

class UserSignUpSerializer(serializers.ModelSerializer):
    """
    Serializer for user signup with password validation
    """
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    confirm_password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    
    # Make email field map to work_email
    work_email = serializers.EmailField(required=True)
    
    # Store validation metadata
    _validation_metadata = {}
    
    class Meta:
        model = User
        fields = [
            'id', 'first_name', 'last_name', 'username', 'password', 'confirm_password',
            'employee_id', 'work_email', 'work_phone', 'work_phone_extension',
            'personal_email', 'personal_phone', 'preferred_language',
            'emergency_contact_name', 'emergency_contact_phone'
        ]
        extra_kwargs = {
            'username': {'required': True},
            'first_name': {'required': True},
            'last_name': {'required': True},
        }
        read_only_fields = ['id']
    
    def validate_username(self, value):
        """Check if username already exists"""
        self._validation_metadata['duplicated_username'] = User.objects.filter(username=value).exists()
        if self._validation_metadata['duplicated_username']:
            raise serializers.ValidationError("Username already exists")
        return value
    
    def validate_work_email(self, value):
        """Check if work_email already exists"""
        self._validation_metadata['duplicated_work_email'] = User.objects.filter(work_email=value).exists()
        if self._validation_metadata['duplicated_work_email']:
            raise serializers.ValidationError("Work email already exists")
        return value
    
    def validate(self, attrs):
        """Validate passwords match and meet requirements"""
        password = attrs.get('password')
        confirm_password = attrs.get('confirm_password')
        
        # Initialize all validation flags
        self._validation_metadata.update({
            'passwords_match': True,
            'password_min_length_error': False,
            'password_no_uppercase_error': False,
            'password_no_lowercase_error': False,
            'password_no_number_error': False,
            'password_no_special_char_error': False,
            'password_valid': True
        })
        
        # Check if passwords match
        if password != confirm_password:
            self._validation_metadata['passwords_match'] = False
            raise serializers.ValidationError({
                "confirm_password": "Passwords don't match"
            })
        
        # Validate each password requirement individually
        validators = [
            ('password_min_length_error', MinimumLengthValidator(min_length=8)),
            ('password_no_uppercase_error', UppercaseValidator()),
            ('password_no_lowercase_error', LowercaseValidator()),
            ('password_no_number_error', NumberValidator()),
            ('password_no_special_char_error', SpecialCharacterValidator(special_characters="!@#$%^&*")),
        ]
        
        password_errors = []
        for flag_name, validator in validators:
            try:
                validator.validate(password)
            except DjangoValidationError as e:
                self._validation_metadata[flag_name] = True
                self._validation_metadata['password_valid'] = False
                password_errors.extend(e.messages if hasattr(e, 'messages') else [str(e)])
        
        if password_errors:
            raise serializers.ValidationError({"password": password_errors})
        
        return attrs
    
    def get_validation_metadata(self):
        """Return the validation metadata"""
        return self._validation_metadata
    
    def create(self, validated_data):
        """Create user with validated password"""
        # Remove confirm_password from validated_data
        validated_data.pop('confirm_password', None)
        password = validated_data.pop('password')
        
        # Generate name from first_name and last_name
        first_name = validated_data.get('first_name', '')
        last_name = validated_data.get('last_name', '')
        validated_data['name'] = f"{first_name} {last_name}".strip()
        
        # Set email to work_email for authentication
        validated_data['email'] = validated_data.get('work_email')
        
        # Create user instance
        user = User.objects.create(**validated_data)
        
        # Set password using Django's password hashing
        user.set_password(password)
        user.save()
        
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for user updates with consistent field structure
    """
    password = serializers.CharField(
        write_only=True, 
        required=False, 
        allow_blank=True,
        style={'input_type': 'password'}
    )
    confirm_password = serializers.CharField(
        write_only=True, 
        required=False,
        allow_blank=True,
        style={'input_type': 'password'}
    )
    
    # Store validation metadata
    _validation_metadata = {}
    
    class Meta:
        model = User
        fields = [
            'id', 'first_name', 'last_name', 'username', 'password', 'confirm_password',
            'employee_id', 'work_email', 'work_phone', 'work_phone_extension',
            'personal_email', 'personal_phone', 'preferred_language',
            'emergency_contact_name', 'emergency_contact_phone'
        ]
        read_only_fields = ['id', 'username']  # Username shouldn't be changed after creation
    
    def validate_work_email(self, value):
        """Check if work_email already exists for other users"""
        user_id = self.instance.id if self.instance else None
        self._validation_metadata['duplicated_work_email'] = User.objects.filter(
            work_email=value
        ).exclude(id=user_id).exists()
        
        if self._validation_metadata['duplicated_work_email']:
            raise serializers.ValidationError("This work email is already registered with another user")
        return value
    
    def validate(self, attrs):
        """Validate passwords if provided"""
        password = attrs.get('password')
        confirm_password = attrs.get('confirm_password')
        
        # Initialize metadata
        self._validation_metadata.update({
            'passwords_match': True,
            'password_min_length_error': False,
            'password_no_uppercase_error': False,
            'password_no_lowercase_error': False,
            'password_no_number_error': False,
            'password_no_special_char_error': False,
            'password_valid': True,
            'password_changed': bool(password)
        })
        
        # If password is provided, validate it
        if password:
            if not confirm_password:
                self._validation_metadata['passwords_match'] = False
                raise serializers.ValidationError({
                    "confirm_password": "Confirm password is required when changing password"
                })
            
            if password != confirm_password:
                self._validation_metadata['passwords_match'] = False
                raise serializers.ValidationError({
                    "confirm_password": "Passwords don't match"
                })
            
            # Validate password requirements
            validators = [
                ('password_min_length_error', MinimumLengthValidator(min_length=8)),
                ('password_no_uppercase_error', UppercaseValidator()),
                ('password_no_lowercase_error', LowercaseValidator()),
                ('password_no_number_error', NumberValidator()),
                ('password_no_special_char_error', SpecialCharacterValidator(special_characters="!@#$%^&*")),
            ]
            
            password_errors = []
            for flag_name, validator in validators:
                try:
                    validator.validate(password)
                except DjangoValidationError as e:
                    self._validation_metadata[flag_name] = True
                    self._validation_metadata['password_valid'] = False
                    password_errors.extend(e.messages if hasattr(e, 'messages') else [str(e)])
            
            if password_errors:
                raise serializers.ValidationError({"password": password_errors})
        
        # Remove confirm_password if password is not being changed
        if not password and 'confirm_password' in attrs:
            attrs.pop('confirm_password')
        
        return attrs
    
    def get_validation_metadata(self):
        """Return the validation metadata"""
        return self._validation_metadata
    
    def update(self, instance, validated_data):
        """Update user with validated data"""
        password = validated_data.pop('password', None)
        validated_data.pop('confirm_password', None)
        
        # Update name if first_name or last_name changed
        if 'first_name' in validated_data or 'last_name' in validated_data:
            first_name = validated_data.get('first_name', instance.first_name)
            last_name = validated_data.get('last_name', instance.last_name)
            validated_data['name'] = f"{first_name} {last_name}".strip()
        
        # Update email to match work_email if changed
        if 'work_email' in validated_data:
            validated_data['email'] = validated_data['work_email']
        
        # Update user fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        # Update password if provided
        if password:
            instance.set_password(password)
            instance.password_changed_on = timezone.now()
        
        instance.save()
        return instance


class UserResponseSerializer(serializers.ModelSerializer):
    """
    Serializer for consistent user response format
    """
    roles = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'employee_id', 'first_name', 'last_name', 'name', 'username',
            'work_email', 'work_phone', 'work_phone_extension',
            'personal_email', 'personal_phone', 'preferred_language',
            'emergency_contact_name', 'emergency_contact_phone', 'roles'
        ]
    
    def get_roles(self, obj):
        """Fetch and format the user's roles"""
        user_roles = obj.userrole_set.all().select_related('role_id')
        return [
            {
                'id': ur.role_id.role_id,
                'name': ur.role_id.name,
                'definition': ur.role_id.definition
            } 
            for ur in user_roles
        ]

class UserToggleStatusSerializer(serializers.Serializer):
    """Serializer for toggling user active status"""
    reason = serializers.CharField(
        required=False, 
        allow_blank=True,
        help_text="Optional reason for status change"
    )

class UserStatusResponseSerializer(serializers.ModelSerializer):
    """Response serializer for user status toggle"""
    roles = serializers.SerializerMethodField()
    active_tickets_count = serializers.IntegerField(read_only=True)
    last_activity_log = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id',
            'username',
            'name',
            'email',
            'is_active',
            'roles',
            'status',
            'last_active',
            'active_tickets_count',
            'last_activity_log',
            'updated_on'
        ]
    
    def get_roles(self, obj):
        user_roles = UserRole.objects.filter(
            user_id=obj
        ).select_related('role_id')
        return [ur.role_id.name for ur in user_roles]
    
    def get_last_activity_log(self, obj):
        last_log = obj.activity_logs.first()
        if last_log:
            return {
                'activity_type': last_log.activity_type,
                'performed_by': last_log.performed_by.name if last_log.performed_by else None,
                'created_on': last_log.created_on,
                'reason': last_log.reason
            }