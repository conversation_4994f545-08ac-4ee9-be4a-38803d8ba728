
import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, IsAdminUser
from django.shortcuts import get_object_or_404

from connectors.line.connector import LineConnector
from connectors.serializers import LineChannelCreateSerializer, LineChannelDetailSerializer, LineChannelListSerializer, LineChannelSoftDeleteSerializer, LineChannelStatusSerializer, LineChannelUpdateSerializer
from connectors.services.channel_service.line_services import LineChannelService

from .models import LineChannel, WhatsAppChannel, FacebookChannel
from .base import ConnectorRegistry
from .admin_serializers import (
    LineChannelSerializer, WhatsAppChannelSerializer, 
    FacebookChannelSerializer
)
from linebot.v3.messaging import Configuration, ApiClient, MessagingApi, BroadcastRequest, TextMessage

logger = logging.getLogger('django.admin')

# class ChannelListView(generics.ListAPIView):
#     """List all channels of a specific type."""
#     permission_classes = [IsAuthenticated]
    
#     def get_queryset(self):
#         channel_type = self.kwargs['channel_type'].lower()
        
#         if channel_type == 'line':
#             return LineChannel.objects.all()
#         elif channel_type == 'whatsapp':
#             return WhatsAppChannel.objects.all()
#         elif channel_type == 'facebook':
#             return FacebookChannel.objects.all()
#         else:
#             return []
    
#     def get_serializer_class(self):
#         channel_type = self.kwargs['channel_type'].lower()
        
#         if channel_type == 'line':
#             return LineChannelSerializer
#         elif channel_type == 'whatsapp':
#             return WhatsAppChannelSerializer
#         elif channel_type == 'facebook':
#             return FacebookChannelSerializer
        
#         # Default serializer
#         return LineChannelSerializer

# class ChannelDetailView(generics.RetrieveUpdateDestroyAPIView):
#     """Get, update or delete a specific channel."""
#     permission_classes = [IsAuthenticated]
    
#     def get_queryset(self):
#         channel_type = self.kwargs['channel_type'].lower()
        
#         if channel_type == 'line':
#             return LineChannel.objects.all()
#         elif channel_type == 'whatsapp':
#             return WhatsAppChannel.objects.all()
#         elif channel_type == 'facebook':
#             return FacebookChannel.objects.all()
#         else:
#             return []
    
#     def get_serializer_class(self):
#         channel_type = self.kwargs['channel_type'].lower()
        
#         if channel_type == 'line':
#             return LineChannelSerializer
#         elif channel_type == 'whatsapp':
#             return WhatsAppChannelSerializer
#         elif channel_type == 'facebook':
#             return FacebookChannelSerializer
        
#         # Default serializer
#         return LineChannelSerializer


# class ChannelCreateView(generics.CreateAPIView):
#     """Create a new channel."""
#     permission_classes = [IsAuthenticated, IsAdminUser]
    
#     def get_serializer_class(self):
#         channel_type = self.kwargs['channel_type'].lower()
        
#         if channel_type == 'line':
#             return LineChannelSerializer
#         elif channel_type == 'whatsapp':
#             return WhatsAppChannelSerializer
#         elif channel_type == 'facebook':
#             return FacebookChannelSerializer
        
#         # Default serializer
#         return LineChannelSerializer
    
#     def perform_create(self, serializer):
#         # Save with current user as creator
#         serializer.save(created_by=self.request.user)


# class ChannelTestConnectionView(APIView):
#     """Test connectivity for a channel."""
#     permission_classes = [IsAuthenticated]
    
#     def post(self, request, channel_type, pk):
#         try:
#             # Get the channel
#             if channel_type.lower() == 'line':
#                 channel = get_object_or_404(LineChannel, pk=pk)
#                 channel_id = channel.channel_id
#             elif channel_type.lower() == 'whatsapp':
#                 channel = get_object_or_404(WhatsAppChannel, pk=pk)
#                 channel_id = channel.phone_number_id
#             elif channel_type.lower() == 'facebook':
#                 channel = get_object_or_404(FacebookChannel, pk=pk)
#                 channel_id = channel.page_id
#             else:
#                 return Response(
#                     {'error': f'Unsupported channel type: {channel_type}'},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
            
#             # Get connector
#             connector = ConnectorRegistry.get_connector(channel_type.lower(), channel_id)
            
#             # Test connection
#             if connector.verify_credentials():
#                 return Response({
#                     'success': True,
#                     'message': 'Connection successful'
#                 })
#             else:
#                 return Response({
#                     'success': False,
#                     'message': 'Connection failed. Please check your credentials.'
#                 }, status=status.HTTP_400_BAD_REQUEST)
                
#         except Exception as e:
#             return Response({
#                 'success': False,
#                 'message': str(e)
#             }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# class ChannelStatusView(APIView):
#     """Get detailed status of a channel."""
#     permission_classes = [IsAuthenticated]
    
#     def get(self, request, channel_type, pk):
#         try:
#             # Get the channel
#             if channel_type.lower() == 'line':
#                 channel = get_object_or_404(LineChannel, pk=pk)
#                 channel_id = channel.channel_id
#             elif channel_type.lower() == 'whatsapp':
#                 channel = get_object_or_404(WhatsAppChannel, pk=pk)
#                 channel_id = channel.phone_number_id
#             elif channel_type.lower() == 'facebook':
#                 channel = get_object_or_404(FacebookChannel, pk=pk)
#                 channel_id = channel.page_id
#             else:
#                 return Response(
#                     {'error': f'Unsupported channel type: {channel_type}'},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
            
#             # Get connector
#             connector = ConnectorRegistry.get_connector(channel_type.lower(), channel_id)
            
#             # Get channel info
#             channel_info = connector.get_channel_info()
            
#             return Response(channel_info)
                
#         except Exception as e:
#             return Response({
#                 'success': False,
#                 'message': str(e)
#             }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        

class ChannelListView(APIView):
    """List all channels of a specific type."""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get(self, request, channel_type):
        try:
            # Add query parameters for filtering
            include_deleted = request.query_params.get('include_deleted', 'false').lower() == 'true'
            status_filter = request.query_params.get('status', None)
            
            if channel_type.lower() == 'line':
                queryset = LineChannel.objects.all()
                
                # Apply filters
                if not include_deleted:
                    queryset = queryset.filter(is_deleted=False)
                    
                if status_filter:
                    queryset = queryset.filter(status=status_filter)
                    
                queryset = queryset.order_by('created_on')
                serializer = LineChannelListSerializer(queryset, many=True)
                
            elif channel_type.lower() == 'whatsapp':
                queryset = WhatsAppChannel.objects.filter(is_deleted=False).order_by('created_on')
                # Use appropriate serializer
                return Response({'error': 'WhatsApp serializer not implemented yet'})
                
            elif channel_type.lower() == 'facebook':
                queryset = FacebookChannel.objects.filter(is_deleted=False).order_by('created_on')
                # Use appropriate serializer
                return Response({'error': 'Facebook serializer not implemented yet'})
                
            else:
                return Response(
                    {'error': f'Invalid channel type: {channel_type}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error listing {channel_type} channels: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )



class ChannelCreateView(APIView):
    """Create a new channel."""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def post(self, request, channel_type):
        try:
            if channel_type.lower() == 'line':
                serializer = LineChannelCreateSerializer(
                    data=request.data,
                    context={'request': request}
                )
                if serializer.is_valid():
                    channel = serializer.save()
                    return Response(
                        LineChannelDetailSerializer(channel).data,
                        status=status.HTTP_201_CREATED
                    )
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                
            else:
                return Response(
                    {'error': f'Create not implemented for {channel_type}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"Error creating {channel_type} channel: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ChannelDetailView(APIView):
    """Get, update, or delete a specific channel."""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get_channel(self, channel_type, pk):
        """Get channel instance based on type."""
        if channel_type.lower() == 'line':
            return get_object_or_404(LineChannel, pk=pk)
        elif channel_type.lower() == 'whatsapp':
            return get_object_or_404(WhatsAppChannel, pk=pk)
        elif channel_type.lower() == 'facebook':
            return get_object_or_404(FacebookChannel, pk=pk)
        else:
            return None
    
    def get(self, request, channel_type, pk):
        """Get channel details."""
        try:
            channel = self.get_channel(channel_type, pk)
            if not channel:
                return Response(
                    {'error': f'Invalid channel type: {channel_type}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            if channel_type.lower() == 'line':
                serializer = LineChannelDetailSerializer(channel)
                return Response(serializer.data)
            else:
                return Response({'error': f'Detail view not implemented for {channel_type}'})
                
        except Exception as e:
            logger.error(f"Error getting {channel_type} channel {pk}: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def put(self, request, channel_type, pk):
        """Update channel."""
        try:
            channel = self.get_channel(channel_type, pk)
            if not channel:
                return Response(
                    {'error': f'Invalid channel type: {channel_type}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            if channel_type.lower() == 'line':
                serializer = LineChannelUpdateSerializer(
                    channel,
                    data=request.data,
                    context={'request': request}
                )
                if serializer.is_valid():
                    updated_channel = serializer.save()
                    return Response(LineChannelDetailSerializer(updated_channel).data)
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({'error': f'Update not implemented for {channel_type}'})
                
        except Exception as e:
            logger.error(f"Error updating {channel_type} channel {pk}: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def patch(self, request, channel_type, pk):
        """Partial update channel."""
        try:
            channel = self.get_channel(channel_type, pk)
            if not channel:
                return Response(
                    {'error': f'Invalid channel type: {channel_type}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            if channel_type.lower() == 'line':
                serializer = LineChannelUpdateSerializer(
                    channel,
                    data=request.data,
                    partial=True,
                    context={'request': request}
                )
                if serializer.is_valid():
                    updated_channel = serializer.save()
                    return Response(LineChannelDetailSerializer(updated_channel).data)
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({'error': f'Partial update not implemented for {channel_type}'})
                
        except Exception as e:
            logger.error(f"Error updating {channel_type} channel {pk}: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def delete(self, request, channel_type, pk):
        """Soft delete channel."""
        try:
            channel = self.get_channel(channel_type, pk)
            if not channel:
                return Response(
                    {'error': f'Invalid channel type: {channel_type}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            if channel_type.lower() == 'line':
                serializer = LineChannelSoftDeleteSerializer(
                    data=request.data,
                    context={'channel': channel, 'request': request}
                )
                if serializer.is_valid():
                    serializer.save()
                    return Response(
                        {'message': 'Channel soft deleted successfully'},
                        status=status.HTTP_200_OK
                    )
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({'error': f'Delete not implemented for {channel_type}'})
                
        except Exception as e:
            logger.error(f"Error deleting {channel_type} channel {pk}: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ChannelStatusView(APIView):
    """Enable or disable a channel."""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def post(self, request, channel_type, pk):
        """Change channel status."""
        try:
            if channel_type.lower() != 'line':
                return Response(
                    {'error': f'Status change not implemented for {channel_type}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            channel = get_object_or_404(LineChannel, pk=pk)
            
            serializer = LineChannelStatusSerializer(
                data=request.data,
                context={'channel': channel, 'request': request}
            )
            
            if serializer.is_valid():
                action = serializer.validated_data['action']
                reason = serializer.validated_data.get('reason')
                
                if action == 'disable':
                    result = LineChannelService.disable_channel(
                        channel=channel,
                        reason=reason,
                        user=request.user
                    )
                else:
                    result = LineChannelService.enable_channel(
                        channel=channel,
                        reason=reason,
                        user=request.user
                    )
                
                if result['success']:
                    return Response(result)
                else:
                    return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            logger.error(f"Error changing {channel_type} channel {pk} status: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ChannelTestConnectionView(APIView):
    """Test channel connection."""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get(self, request, channel_type, pk):
        try:
            if channel_type.lower() == 'line':
                channel = get_object_or_404(LineChannel, pk=pk)
                
                # Create connector instance
                connector = LineConnector(
                    channel_id=channel.channel_id,
                    channel=channel
                )
                
                # Test connection
                is_valid = connector.verify_credentials()
                
                if is_valid:
                    # Get channel info
                    channel_info = connector.get_channel_info()
                    return Response({
                        'success': True,
                        'message': 'Connection successful',
                        'channel_info': channel_info
                    })
                else:
                    return Response({
                        'success': False,
                        'message': 'Connection failed'
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            else:
                return Response(
                    {'error': f'Test not implemented for {channel_type}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"Error testing {channel_type} channel {pk}: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ChannelBroadcastView(APIView):
    """Send broadcast message through a channel."""
    permission_classes = [IsAuthenticated]
    
    def post(self, request, channel_type, pk):
        """Send custom broadcast message."""
        try:
            if channel_type.lower() != 'line':
                return Response(
                    {'error': f'Broadcast not implemented for {channel_type}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            channel = get_object_or_404(LineChannel, pk=pk)
            
            # Get message from request
            message = request.data.get('message')
            if not message:
                return Response(
                    {'error': 'Message is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get customers for this channel
            customers = LineChannelService.get_channel_customers(channel)
            
            # Send broadcast
            configuration = Configuration(access_token=channel.channel_access_token)
            
            with ApiClient(configuration) as api_client:
                api_instance = MessagingApi(api_client)
                
                broadcast_request = BroadcastRequest(
                    messages=[TextMessage(text=message)]
                )
                
                response = api_instance.broadcast(broadcast_request)
                
                return Response({
                    'success': True,
                    'message': 'Broadcast sent successfully',
                    'recipients': len(customers)
                })
                
        except Exception as e:
            logger.error(f"Error broadcasting on {channel_type} channel {pk}: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
